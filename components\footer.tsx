import Link from "next/link";
import NewsletterSubscribe from "./NewsletterSubscribe";
import { useTranslations } from 'next-intl';

export default function Footer() {
  const t = useTranslations('Footer');
  return (
    <footer className="bg-purple-950 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Top section with columns */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 mb-12">
          {/* Column 1: References */}
          <div className="text-center sm:text-left">
            <h4 className="font-semibold mb-6 text-white text-lg">{t('references')}</h4>
            <ul className="space-y-3 text-purple-100/80">
              <li><Link href="/our-approach" className="hover:text-white transition-colors">{t('approach')}</Link></li>
              <li><Link href="/about" className="hover:text-white transition-colors">{t('aboutUs')}</Link></li>
              <li><Link href="/testimonials" className="hover:text-white transition-colors">{t('testimonials')}</Link></li>
              <li><Link href="/faq" className="hover:text-white transition-colors">{t('faq')}</Link></li>
              <li><Link href="/contact" className="hover:text-white transition-colors">{t('contactUs')}</Link></li>
            </ul>
          </div>

          {/* Column 2: Services */}
          <div className="text-center sm:text-left">
            <h4 className="font-semibold mb-6 text-white text-lg">{t('services')}</h4>
            <ul className="space-y-3 text-purple-100/80">
              <li><Link href="/website-development" className="hover:text-white transition-colors">{t('websiteDevelopment')}</Link></li>
              <li><Link href="/chatbot-integration" className="hover:text-white transition-colors">{t('chatbotIntegration')}</Link></li>
            </ul>
          </div>

          {/* Column 3: Social networks */}
          <div className="text-center sm:text-left">
            <h4 className="font-semibold mb-6 text-white text-lg">{t('socialNetworks')}</h4>
            <ul className="space-y-3 text-purple-100/80">
              <li><a href="#" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors">{t('linkedin')}</a></li>
            </ul>
          </div>

          {/* Column 4: Subscribe */}
          <div className="text-center sm:text-left">
            <h4 className="font-semibold mb-6 text-white text-lg">{t('newsletter')}</h4>
            <NewsletterSubscribe
              containerClassName="max-w-xs mx-auto sm:mx-0 mb-2"
              inputClassName="bg-purple-900/50 border-purple-700/50 text-white placeholder:text-purple-100/50 focus:border-purple-500 focus:ring-purple-500"
              buttonClassName="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white"
              buttonIcon={true}
            />
          </div>
        </div>

        {/* Bottom section with logo and copyright */}
        <div className="border-t border-purple-800/30 pt-8 flex flex-col md:flex-row justify-center md:justify-between items-center">
          <div className="mb-4 md:mb-0">
             <Link href="/" className="flex items-center justify-center md:justify-start">
               <span className="text-2xl font-bold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                 UpZera
               </span>
             </Link>
          </div>
          <p className="text-purple-100/60 text-sm text-center md:text-right">
            {t('copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
}
