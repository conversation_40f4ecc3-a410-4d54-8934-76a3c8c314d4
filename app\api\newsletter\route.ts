import { NextResponse } from 'next/server';
import { getEmailService, createNewsletterNotificationEmail, createNewsletterConfirmationEmail } from '@/lib/email-service';

export async function GET() {
  return NextResponse.json({ message: 'Newsletter API endpoint. Use POST to subscribe.' });
}

export async function POST(request: Request) {
  const { email } = await request.json();

  // Basic validation
  if (!email || !/\S+@\S+\.\S+/.test(email)) {
    return NextResponse.json(
      { error: 'Valid email is required' },
      { status: 400 }
    );
  }

  try {
    const emailService = getEmailService();

    // Create notification email for admin team
    const notificationHtml = createNewsletterNotificationEmail(email);

    // Create confirmation email for subscriber
    const confirmationHtml = createNewsletterConfirmationEmail();

    // Send both emails
    await emailService.sendMultipleEmails([
      {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: 'New Newsletter Subscription',
        html: notificationHtml
      },
      {
        to: email,
        subject: 'Welcome to UpZera Newsletter!',
        html: confirmationHtml
      }
    ]);

    return NextResponse.json({
      message: 'Subscription successful',
      success: true
    });
  } catch (error) {
    console.error('Error processing newsletter subscription:', error);
    return NextResponse.json(
      {
        error: 'Failed to process subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
