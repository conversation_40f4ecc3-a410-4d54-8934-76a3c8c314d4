import { NextResponse } from 'next/server';
import { getEmailService, createContactFormNotificationEmail, createContactFormConfirmationEmail } from '@/lib/email-service';

export async function GET() {
  return NextResponse.json({ message: 'Contact API endpoint. Use POST to submit contact form.' });
}

export async function POST(request: Request) {
  const { name, email, service, message } = await request.json();

  // Basic validation
  if (!name || !email || !service || !message) {
    return NextResponse.json(
      { error: 'All fields are required' },
      { status: 400 }
    );
  }

  if (!/\S+@\S+\.\S+/.test(email)) {
    return NextResponse.json(
      { error: 'Valid email is required' },
      { status: 400 }
    );
  }

  try {
    const emailService = getEmailService();

    // Create notification email for admin team
    const notificationHtml = createContactFormNotificationEmail({
      name,
      email,
      service,
      message
    });

    // Create confirmation email for client
    const confirmationHtml = createContactFormConfirmationEmail(name);

    // Send both emails
    await emailService.sendMultipleEmails([
      {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: `New Contact Form Submission!! - ${service}`,
        html: notificationHtml
      },
      {
        to: email,
        subject: 'Thank you for contacting UpZera!',
        html: confirmationHtml
      }
    ]);

    return NextResponse.json({
      message: 'Email sent successfully',
      success: true
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      {
        error: 'Failed to send email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}